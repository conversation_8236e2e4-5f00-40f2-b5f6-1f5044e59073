import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/providers/navigation_provider.dart';
import 'package:culture_connect/services/auto_lock_service.dart';
import 'package:culture_connect/widgets/custom_bottom_navigation.dart';
import 'package:culture_connect/screens/home_screen.dart';
import 'package:culture_connect/screens/explore_screen.dart';
import 'package:culture_connect/screens/bookings_screen.dart';
import 'package:culture_connect/screens/messaging/chat_list_screen.dart';
import 'package:culture_connect/screens/profile_screen.dart';

class MainNavigation extends ConsumerStatefulWidget {
  const MainNavigation({super.key});

  @override
  ConsumerState<MainNavigation> createState() => _MainNavigationState();
}

class _MainNavigationState extends ConsumerState<MainNavigation> {
  @override
  void initState() {
    super.initState();
    // Initialize auto-lock service
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(autoLockServiceProvider);
    });
  }

  @override
  Widget build(BuildContext context) {
    final currentIndex = ref.watch(navigationProvider);
    final autoLockService = ref.watch(autoLockServiceProvider);

    return GestureDetector(
      // Record user activity on any interaction
      onTap: () => autoLockService.recordActivity(),
      onPanDown: (_) => autoLockService.recordActivity(),
      onScaleStart: (_) => autoLockService.recordActivity(),
      child: Scaffold(
        body: IndexedStack(
          index: currentIndex.index,
          children: const [
            HomeScreen(),
            ExploreScreen(),
            BookingsScreen(),
            ChatListScreen(),
            ProfileScreen(),
          ],
        ),
        bottomNavigationBar: const CustomBottomNavigation(),
      ),
    );
  }
}
